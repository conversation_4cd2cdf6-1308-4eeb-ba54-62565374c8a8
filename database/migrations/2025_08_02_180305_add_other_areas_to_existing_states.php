<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Cargo\Entities\State;
use Modules\Cargo\Entities\Area;

class AddOtherAreasToExistingStates extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Add "Other" areas for all existing covered states
        $states = State::where('covered', 1)->get();

        foreach ($states as $state) {
            // Check if "Other" area already exists for this state
            $existingOtherArea = Area::where('state_id', $state->id)
                ->where(function($query) {
                    $query->where('name', 'like', '%other%')
                          ->orWhere('name', 'like', '%أخرى%'); // Arabic for "other"
                })
                ->first();

            if (!$existingOtherArea) {
                // Create "Other" area for this state
                Area::create([
                    'state_id' => $state->id,
                    'country_id' => $state->country_id,
                    'name' => json_encode([
                        'en' => 'Other',
                        'ar' => 'أخرى'
                    ])
                ]);
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Remove all "Other" areas
        Area::where(function($query) {
            $query->where('name', 'like', '%other%')
                  ->orWhere('name', 'like', '%أخرى%');
        })->delete();
    }
}
